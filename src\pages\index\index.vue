<template>
  
</template>

<script>
import { reactive, onMounted } from "vue";
import Taro from "@tarojs/taro";

export default {
  setup() {
    const data = reactive({
      
    });
    const open = url => {
      Taro.navigateTo({
        url: "/pages/test/index?url=" + encodeURIComponent(url)
      });
    };
    const call = num => {
      Taro.makePhoneCall({
        phoneNumber: num
      });
    };

    onMounted(() => {
      
    });
    return { data, open, call };
  }
};
</script>

<style>

</style>
